{
    "name": "ivampiresp/flarum-s",
    "description": "Asynchronous PHP web servers for Flarum.",
    "type": "library",
    "minimum-stability": "beta",
    "prefer-stable": true,
    "keywords": ["flarum", "discussion", "php", "swoole", "async", "performance"],
    "homepage": "https://flarum.org/",
    "license": "MIT",
    "support": {
        "issues": "https://github.com/ivampiresp/flarum-s/issues",
        "source": "https://github.com/ivampiresp/flarum-s/"
    },
    "authors": [
        {
            "name": "Trint",
            "email": "<EMAIL>"
        },
        {
            "name": "<PERSON><PERSON>",
            "email": "<EMAIL>",
            "role": "<PERSON><PERSON><PERSON>"
        },
        {
            "name": "iVampireSP",
            "email": "<EMAIL>",
        }
    ],
    "require": {
        "php": "*",
        "ext-swoole": "*",
        "laminas/laminas-diactoros": "^2.26",
        "laminas/laminas-httphandlerrunner": "^2.9"
    },
    "autoload": {
      "psr-4": {
          "FlarumS\\": "src/"
      }
    }
}
